import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

class ChartEmptyStateBuilder {
  const ChartEmptyStateBuilder();

  Widget buildNoDataWidget(
    BuildContext context, {
    String? message,
    Widget? title,
  }) {
    return Column(
      children: [
        if (title != null) title,
        Expanded(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 122.r),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Symbols.monitoring,
                    size: 40.r,
                    color: const Color(0xFFFFB522),
                  ),
                  SizedBox(height: 16.r),
                  Text(
                    'There is no data available at the moment',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
