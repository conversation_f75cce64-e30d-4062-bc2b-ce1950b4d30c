import 'package:flutter/material.dart';

/// Legend position options for dual-axis charts
enum LegendPosition {
  above,
  below,
}

/// Data model for dual-axis chart
class DualAxisChartData {
  const DualAxisChartData({
    required this.labels,
    required this.leftAxisData,
    required this.rightAxisData,
    this.leftAxisLabel = 'Clicks',
    this.rightAxisLabel = 'Conversions',
  });

  final List<String> labels;
  final List<num> leftAxisData;
  final List<num> rightAxisData;
  final String leftAxisLabel;
  final String rightAxisLabel;
}

/// Configuration for dual-axis chart appearance and behavior
class DualAxisChartConfig {
  const DualAxisChartConfig({
    this.showLegend = true,
    this.legendPosition = LegendPosition.above,
    this.showTooltip = true,
    this.showGrid = true,
    this.aspectRatio = 1.5,
    this.xAxisLabelInterval = 1,
    this.yAxisGridLines = 6,
    this.leftAxisColor,
    this.rightAxisColor,
    this.tooltipBackgroundColor,
  });

  final bool showLegend;
  final LegendPosition legendPosition;
  final bool showTooltip;
  final bool showGrid;
  final double aspectRatio;
  final int xAxisLabelInterval;
  final int yAxisGridLines;
  final Color? leftAxisColor;
  final Color? rightAxisColor;
  final Color? tooltipBackgroundColor;
}

/// Chart scaling information for dual-axis charts
class ChartScaling {
  const ChartScaling({
    required this.leftMaxY,
    required this.rightMaxY,
    required this.leftInterval,
    required this.rightInterval,
  });

  final double leftMaxY;
  final double rightMaxY;
  final double leftInterval;
  final double rightInterval;
}

/// Chart type options for campaign charts
enum CampaignChartType {
  horizontalBar,
  line,
}

/// Data model for campaign chart
class CampaignChartData {
  const CampaignChartData({
    required this.items,
    this.valueLabel = 'Value',
    this.nameLabel = 'Name',
  });

  final List<CampaignChartItem> items;
  final String valueLabel;
  final String nameLabel;
}

/// Individual campaign chart item
class CampaignChartItem {
  const CampaignChartItem({
    required this.name,
    required this.value,
    this.id,
    this.color,
  });

  final String name;
  final num value;
  final int? id;
  final Color? color;
}

/// Configuration for campaign chart appearance and behavior
class CampaignChartConfig {
  const CampaignChartConfig({
    this.chartType = CampaignChartType.horizontalBar,
    this.showTitle = true,
    this.title = '',
    this.showTooltip = true,
    this.showGrid = true,
    this.aspectRatio = 1.5,
    this.maxItems = 10,
    this.sortAscending = true,
    this.primaryColor,
    this.backgroundColor,
    this.borderColor,
    this.titleStyle,
    this.labelStyle,
    this.showValues = false,
    this.showLegend = false,
    this.legendPosition = LegendPosition.above,
    this.padding,
    this.margin,
    this.yAxisGridLines = 5,
    this.xAxisLabelInterval = 1,
    this.maxLabelLength = 10,
    this.labelTruncationSuffix = '...',
    this.rotateLabels = true,
  });

  final CampaignChartType chartType;
  final bool showTitle;
  final String title;
  final bool showTooltip;
  final bool showGrid;
  final double aspectRatio;
  final int maxItems;
  final bool sortAscending;
  final Color? primaryColor;
  final Color? backgroundColor;
  final Color? borderColor;
  final TextStyle? titleStyle;
  final TextStyle? labelStyle;
  final bool showValues;
  final bool showLegend;
  final LegendPosition legendPosition;

  final EdgeInsets? padding;
  final EdgeInsets? margin;

  /// Number of grid lines to show (default: 5)
  final int yAxisGridLines;

  /// Interval for showing X-axis labels (1 = show all, 2 = show every other, etc.)
  final int xAxisLabelInterval;

  /// Maximum length for labels before truncation (default: 10)
  final int maxLabelLength;

  /// Suffix to add when truncating labels (default: '...')
  final String labelTruncationSuffix;

  /// Whether to rotate labels to prevent overlap (default: true)
  final bool rotateLabels;
}
